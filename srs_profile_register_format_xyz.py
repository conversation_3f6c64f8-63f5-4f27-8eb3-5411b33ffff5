
'''

这个文件和src_profile_register.py类似， 但是的格式是xyz


这个程序的目的是是先粗匹配， 然后再精匹配
1 点云， 模板和场景一起显示
2 选择点， 场景和模板对应的点
3 完成粗匹配和精匹配

'''


import open3d as o3d
import numpy as np
import copy
import matplotlib.pyplot as plt
import logging
import os

import sys



def pick_points(pcd):
    """
    手动选择点云中的点
    返回选择的点的索引
    """
    print("请选择4个点 (按Shift+左键选择点, Q退出)")
    vis = o3d.visualization.VisualizerWithEditing()
    vis.create_window()
    vis.add_geometry(pcd)
    vis.run()  # 用户交互选点
    vis.destroy_window()
    return vis.get_picked_points()


def save_init_matrix(matrix, filename):
    
    # Add _keypoint before the extension
    filename_parts = filename.rsplit('.', 1)
    filename_parts[1] = ".txt"
    filename = filename_parts[0] + '_init_trans_matrix' + filename_parts[1]
    print("filename: ", filename)
    np.savetxt(filename, matrix, fmt='%.6f')
   

def manual_registration(concate_pcd):
    """
    手动选择对应点进行初始配准
    """
    print("1选择 ==源点云==中的4个点:")
    picked_idx = pick_points(concate_pcd)
    print("选择==目标点云==中对应的4个点:")
    source_picked_idx = picked_idx[:4]
    target_picked_idx = picked_idx[4:]
    print(f"source_picked_idx: {source_picked_idx}, target_picked_idx: {target_picked_idx}")
    # target_picked_idx = pick_points(target)
    
    # assert(len(source_picked_idx) >= 4 and len(target_picked_idx) >= 4)
    # assert(len(source_picked_idx) == len(target_picked_idx))
    
    # 获取选择的点的坐标
    source_points = np.asarray(concate_pcd.points)[source_picked_idx]
    target_points = np.asarray(concate_pcd.points)[target_picked_idx]
    

    print(source_points)
    print(target_points)   
    
    
    # 使用SVD求解刚体变换
    center_source = np.mean(source_points, axis=0)
    center_target = np.mean(target_points, axis=0)
    source_centered = source_points - center_source
    target_centered = target_points - center_target
    H = np.dot(source_centered.T, target_centered)
    U, S, Vt = np.linalg.svd(H)
    R = np.dot(Vt.T, U.T)
    if np.linalg.det(R) < 0:
        print("R is not a rotation matrix")
        Vt[2,:] *= -1
        R = np.dot(Vt.T, U.T)
    t = center_target - np.dot(R, center_source)
    trans_init = np.eye(4)
    trans_init[:3,:3] = R
    trans_init[:3,3] = t

    print(trans_init)   
    # 显示源点云和目标点云中选择的对应点
    source_points_pcd = o3d.geometry.PointCloud()
    target_points_pcd = o3d.geometry.PointCloud()
    
    source_points_pcd.points = o3d.utility.Vector3dVector(source_points)
    target_points_pcd.points = o3d.utility.Vector3dVector(target_points)

    print('disp source_points_pcd')
    o3d.visualization.draw_geometries([source_points_pcd, target_points_pcd]) 

    source_points_pcd_trans = copy.deepcopy(source_points_pcd)
    source_points_pcd_trans.transform(trans_init)
    # 设置点的颜色和大小
    source_points_pcd_trans.paint_uniform_color([1, 0, 0])  # 红色
    target_points_pcd.paint_uniform_color([0, 1, 0])  # 绿色
    
    # 可视化选择的对应点
    o3d.visualization.draw_geometries([source_points_pcd_trans, target_points_pcd])
    
    return trans_init

def icp_registration(source, target, trans_init, max_correspondence_distance=2.0):
    """
    使用ICP进行精细配准
    """
    # print("应用点到点ICP配准")
    reg_p2p = o3d.pipelines.registration.registration_icp(
        source, target, max_correspondence_distance, trans_init,
        o3d.pipelines.registration.TransformationEstimationPointToPoint(),
        o3d.pipelines.registration.ICPConvergenceCriteria(
            relative_fitness=1e-6,      # 相对适应度阈值
            relative_rmse=1e-6,
            max_iteration=2000))
    
    return reg_p2p.transformation


def save_plt(filename):
    
    filename_parts = filename.rsplit('.', 1)
    filename_parts[1] = ".jpg"
    filename = filename_parts[0] + '_result'

    plt.savefig(filename)




def distance_near_point_sick(model, source, savename='x.jpg'):
    dist = source.compute_point_cloud_distance(model) # 2025年2月26日11:26:07， 这条语句才是以source为基准

    n_model = len(model.points)
    n_source = len(source.points)
    n_dist = len(dist)
    # print(f'n_model: {n_model}, n_source: {n_source}, n_dist: {n_dist}')
    
    idx= [i for i,distance in enumerate(dist)  if distance<1.0 ]
    N = len(idx)
    # print(f'N: {N}')
   
    filter_ratio = (1 - N/n_source)*100
    # print(f'filter_ratio: {filter_ratio}%')
    
    if N<1:
        logging.info('没有相邻的点')
        return
    #最后将点云中相同的部分和不同的部分分别取出来进行显示
    same_part = source.select_by_index(idx)
    diff_part = source.select_by_index(idx,invert=True)
    same_part.paint_uniform_color([0,1,0])
    diff_part.paint_uniform_color([1,0,0])  

    model_tree =  o3d.geometry.KDTreeFlann(model)
    

    a_left = np.zeros((N,3)) # model 的点   
    a_right = np.zeros((N,3)) # source 的点
    distance = np.zeros(N)

  
    for i in range(N):
        index2 = idx[i]
        point2 = source.points[index2]

        a_right[i,:] =  point2  # 参考基准  以右边为准
        k = 1  # 只去搜索一个
        [k, Index, _] = model_tree.search_knn_vector_3d(point2,k)    
        index1 = Index[0]  # 
        point1 = model.points[index1]  
        a_left[i,:] = point1
        distance[i] = np.linalg.norm(point1 - point2)

    
    source_points = np.asarray(source.points)
    target_points = np.asarray(model.points)


    # 统计距离大于0.2的点个数
    N_far = len([i for i,distance in enumerate(distance)  if distance>0.2 ])
    # print(f'距离大于0.2的点个数为：{N_far}, 比例为：{N_far/N}')
    # 分别以x z轴显示相邻点的距离
    x,y,z = a_left[:,0], a_left[:,1], a_left[:,2]    


    # 在同一个figure中显示两个子图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 左侧子图显示点云投影
    ax1.scatter(source_points[:, 0], source_points[:, 1], c='red', label='Source', alpha=0.6, s=1)
    ax1.scatter(target_points[:, 0], target_points[:, 1], c='green', label='Target', alpha=0.6, s=1)
    ax1.set_xlabel('X axis')
    ax1.set_ylabel('Z axis')
    ax1.set_title('Point Cloud X-Z Projection')
    ax1.legend()
    ax1.grid(True)

    # 右侧子图显示距离误差
    ax2.plot(x, distance, 'b.')
    ax2.set_title(f'filter_ratio = {filter_ratio:.2f}% error(< 0.2mm) percent={100-100.0*N_far/N:.2f}%')
    ax2.grid(True)

    print(f'percent={100-100.0*N_far/N:.2f}%')

    plt.tight_layout()    
    save_plt(savename)
    plt.show()

 


def load_init_matrix(filename):
    # Add _init_trans_matrix before the extension
    base, ext = os.path.splitext(filename)
    filename = base + '_init_trans_matrix' + '.txt'
    print("filename: ", filename)
    return np.loadtxt(filename)

def main():       
    

    gen_init_matrix = False  # True: 鼠标手动选择轮廓与标轨对应的特征点

    file_idx = 2  # 数据组别
    color_str = 'blue'  #轮廓以颜色区分
    filename_xyz = f'{color_str}_filtered.xyz'

    path =  f"./20250730/{file_idx}/"
    
    filename_trans_init = f'{color_str}_filtered_init_trans_matrix.txt'

    source_filename = path + filename_xyz
    points_xyz = np.loadtxt(source_filename)
    source = o3d.geometry.PointCloud()
    source.points = o3d.utility.Vector3dVector(points_xyz)

    
    model_filename = 'rail-3.xyz'
    points_xyz = np.loadtxt(model_filename)
    target = o3d.geometry.PointCloud()
    target.points = o3d.utility.Vector3dVector(points_xyz)
    


    # 可视化原始点云
    source.paint_uniform_color([1, 0, 0])  # 红色
    target.paint_uniform_color([0, 1, 0])  # 绿色
    # o3d.visualization.draw_geometries([source, target])  
        
    
    
    # 手动选择对应点进行初始配准
    concate_pcd = source + target
    init_matrix_filename = copy.copy(source_filename)
    
    if gen_init_matrix:
        trans_init = manual_registration(concate_pcd)    
        save_init_matrix(trans_init, init_matrix_filename)
        sys.exit()     
    

    
    trans_init = np.loadtxt(path + filename_trans_init)
    source_temp = copy.deepcopy(source)
    source_temp.transform(trans_init)
    print("初始配准完成")
    # o3d.visualization.draw_geometries([source_temp, target])
    
    # ICP精细配准
    trans_icp = icp_registration(source, target, trans_init)
    source_temp = copy.deepcopy(source)
    source_temp.transform(trans_icp)
    print("ICP配准完成")
    
    # 可视化最终结果
    # o3d.visualization.draw_geometries([source_temp, target])

    save_plt_name = copy.copy(source_filename)
    

    distance_near_point_sick(target,source_temp, save_plt_name)
    


   

if __name__ == "__main__":
    main()